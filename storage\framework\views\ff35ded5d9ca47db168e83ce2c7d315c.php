<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['color' => null, 'vertical' => false, 'counter' => false]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['color' => null, 'vertical' => false, 'counter' => false]); ?>
<?php foreach (array_filter((['color' => null, 'vertical' => false, 'counter' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
    $classes = Arr::toCssClasses([
        'steps' => true,
        "steps-$color" => $color,
        'steps-vertical' => $vertical,
        'steps-counter' => $counter,
    ]);
?>

<ul <?php echo e($attributes->class($classes)); ?>><?php echo e($slot); ?></ul>
<?php /**PATH G:\DL\app\app\platform/core/base/resources/views/components/step/index.blade.php ENDPATH**/ ?>