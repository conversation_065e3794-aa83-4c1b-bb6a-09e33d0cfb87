<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag; ?>
<?php foreach($attributes->onlyProps(['isActive' => false]) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $attributes = $attributes->exceptProps(['isActive' => false]); ?>
<?php foreach (array_filter((['isActive' => false]), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
} ?>
<?php $__defined_vars = get_defined_vars(); ?>
<?php foreach ($attributes as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
} ?>
<?php unset($__defined_vars); ?>

<?php
    $classes = Arr::toCssClasses([
        'step-item' => true,
        'active' => $isActive,
    ]);
?>

<li <?php echo e($attributes->class($classes)); ?>><?php echo e($slot); ?></li>
<?php /**PATH G:\DL\app\app\platform/core/base/resources/views/components/step/item.blade.php ENDPATH**/ ?>